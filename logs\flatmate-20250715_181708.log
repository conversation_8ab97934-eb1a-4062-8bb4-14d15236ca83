2025-07-15 18:17:08 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 18:17:08 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 18:17:08 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-15 18:17:08 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-15 18:17:08 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-15 18:17:08 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-15 18:17:09 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-15 18:17:09 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 18:17:09 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-15 18:17:09 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-15 18:17:09 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-15 18:17:09 - [flatmate.src.fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-15 18:17:09 - [main] [INFO] - Application starting...
2025-07-15 18:17:11 - [main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-15 18:17:11 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-15 18:17:11 - [flatmate.src.fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-15 18:17:11 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Factories
2025-07-15 18:17:11 - [flatmate.src.fm.module_coordinator] [DEBUG] - Registered module factories: ['home', 'update_data', 'categorize']
2025-07-15 18:17:11 - [main] [INFO] - 
=== Initializing Database Cache ===
2025-07-15 18:17:11 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 18:17:11 - [flatmate.src.fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 373MB cache limit
2025-07-15 18:17:11 - [flatmate.src.fm.core.data_services.cache.db_caching] [INFO] - Initializing database cache...
2025-07-15 18:17:11 - [flatmate.src.fm.core.data_services.cache.db_caching] [INFO] - Loading all transactions from database with all columns...
2025-07-15 18:17:11 - [flatmate.src.fm.core.data_services.cache.db_caching] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory, 0.6s
2025-07-15 18:17:11 - [main] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory usage
2025-07-15 18:17:11 - [flatmate.src.fm.module_coordinator] [INFO] - Starting Application
2025-07-15 18:17:11 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Presenter
2025-07-15 18:17:11 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-15 18:17:11 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Module
2025-07-15 18:17:11 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-15 18:17:12 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Module initialization complete
2025-07-15 18:17:12 - [main] [INFO] - 
=== Application Ready ===
2025-07-15 18:17:18 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Module cleanup complete
2025-07-15 18:17:19 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 18:17:19 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 382MB cache limit
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-07-15 18:17:19 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 18:17:19 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 382MB cache limit
2025-07-15 18:17:19 - [fm.modules.base.base_module_view] [INFO] - Setting up CatView in Main Window
2025-07-15 18:17:19 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-15 18:17:19 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-15 18:17:19 - [fm.modules.base.base_module_view] [INFO] - CatView setup complete
2025-07-15 18:17:19 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-15 18:17:19 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 18:17:19 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_account = None (Source: cat_presenter.py:initialize)
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Auto-loading from database with last query/filters...
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Restoring last used filters: {'start_date': datetime.date(2024, 7, 15), 'end_date': datetime.date(2025, 7, 15)}
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from database for categorisation…
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Filters: {'start_date': datetime.date(2024, 7, 15), 'end_date': datetime.date(2025, 7, 15)}
2025-07-15 18:17:19 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.database.last_start_date = 2024-07-15 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 18:17:19 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.database.last_start_date = 2024-07-15
2025-07-15 18:17:19 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.database.last_end_date = 2025-07-15 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 18:17:19 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.database.last_end_date = 2025-07-15
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Saved last used filters for next session
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {'start_date': datetime.date(2024, 7, 15), 'end_date': datetime.date(2025, 7, 15)}
2025-07-15 18:17:19 - [fm.core.data_services.cache.db_caching] [DEBUG] - Cache not available, falling back to database query with filters: {'start_date': datetime.date(2024, 7, 15), 'end_date': datetime.date(2025, 7, 15)}
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Retrieved 884 transactions as DataFrame
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame shape: (884, 30), empty: False
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'import_date', 'modified_date', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id']
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - First few rows:
              account  amount  balance  ... tp_part  tp_ref unique_id
0  38-9004-0646977-04  -30.44    32.13  ...    None    None      None
1  38-9004-0646977-04   -9.40    22.73  ...    None    None      None
2  38-9004-0646977-04  -18.99    83.07  ...    None    None      None
3  38-9004-0646977-04  -20.50    62.57  ...    None    None      None
4  38-9004-0646977-00 -100.00   609.10  ...    None    None      None

[5 rows x 30 columns]
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Using DataFrame with shape: (884, 30)
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Applying categorization to transactions...
2025-07-15 18:17:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [ERROR] - Error loading transactions: 'CategorizeConfig' object has no attribute 'get'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 375, in _handle_load_db
    df = self._apply_default_sorting(df)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 410, in _apply_default_sorting
    sort_column = config.get('categorize.display.default_sort_column', 'date')
                  ^^^^^^^^^^
AttributeError: 'CategorizeConfig' object has no attribute 'get'
