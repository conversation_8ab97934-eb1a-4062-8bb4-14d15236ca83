2025-07-15 17:37:58 - [flatmate.src.fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 17:37:58 - [flatmate.src.fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 17:37:58 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 17:37:58 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 17:37:59 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-15 17:37:59 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-15 17:37:59 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-15 17:37:59 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-15 17:37:59 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-15 17:37:59 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 17:37:59 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-15 17:37:59 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-15 17:37:59 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-15 17:37:59 - [flatmate.src.fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-15 17:37:59 - [main] [INFO] - Application starting...
2025-07-15 17:38:02 - [main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-15 17:38:02 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-15 17:38:02 - [flatmate.src.fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-15 17:38:02 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Factories
2025-07-15 17:38:02 - [flatmate.src.fm.module_coordinator] [DEBUG] - Registered module factories: ['home', 'update_data', 'categorize']
2025-07-15 17:38:02 - [main] [INFO] - 
=== Preloading Database ===
2025-07-15 17:38:02 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 17:38:02 - [main] [WARNING] - Database preload failed: 'DBIOService' object has no attribute 'column_manager' - modules will load data on demand
2025-07-15 17:38:02 - [flatmate.src.fm.module_coordinator] [INFO] - Starting Application
2025-07-15 17:38:02 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Presenter
2025-07-15 17:38:02 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-15 17:38:02 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Module
2025-07-15 17:38:02 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-15 17:38:02 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Module initialization complete
2025-07-15 17:38:02 - [main] [INFO] - 
=== Application Ready ===
2025-07-15 17:38:12 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Module cleanup complete
2025-07-15 17:38:12 - [flatmate.src.fm.modules.update_data.ud_presenter] [DEBUG] - Signals connected
2025-07-15 17:38:12 - [flatmate.src.fm.modules.base.base_module_view] [INFO] - Setting up UpdateDataView in Main Window
2025-07-15 17:38:12 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-15 17:38:12 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-15 17:38:12 - [flatmate.src.fm.modules.base.base_module_view] [INFO] - UpdateDataView setup complete
2025-07-15 17:38:12 - [flatmate.src.fm.modules.update_data.ud_presenter] [DEBUG] - ud_presenter called self.view.setup_in_main_window
2025-07-15 17:38:16 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Presenter
2025-07-15 17:38:16 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-15 17:38:16 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Module
2025-07-15 17:38:16 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-15 17:38:16 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Module initialization complete
2025-07-15 17:38:18 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Module cleanup complete
2025-07-15 17:38:18 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 17:38:19 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-07-15 17:38:19 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-07-15 17:38:19 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-07-15 17:38:19 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-07-15 17:38:19 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-07-15 17:38:19 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-07-15 17:38:19 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 17:38:19 - [fm.modules.base.base_module_view] [INFO] - Setting up CatView in Main Window
2025-07-15 17:38:19 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-15 17:38:19 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-15 17:38:19 - [fm.modules.base.base_module_view] [INFO] - CatView setup complete
2025-07-15 17:38:19 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-15 17:38:19 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 17:38:19 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_account = None (Source: cat_presenter.py:initialize)
2025-07-15 17:38:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Auto-loading from database with last query/filters...
2025-07-15 17:38:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Restoring last used filters: {'start_date': datetime.date(2024, 7, 15), 'end_date': datetime.date(2025, 7, 15)}
2025-07-15 17:38:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from database for categorisation…
2025-07-15 17:38:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Filters: {'start_date': datetime.date(2024, 7, 15), 'end_date': datetime.date(2025, 7, 15)}
2025-07-15 17:38:19 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.database.last_start_date = 2024-07-15 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 17:38:19 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.database.last_start_date = 2024-07-15
2025-07-15 17:38:19 - [fm.core.config.base_local_config_v2] [DEBUG] - Saved user preference categorize.database.last_end_date = 2025-07-15 to C:\Users\<USER>\.flatmate\preferences.yaml
2025-07-15 17:38:19 - [fm.core.config.base_local_config_v2] [INFO] - Set user preference: categorize.database.last_end_date = 2025-07-15
2025-07-15 17:38:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Saved last used filters for next session
2025-07-15 17:38:19 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {'start_date': datetime.date(2024, 7, 15), 'end_date': datetime.date(2025, 7, 15)}
2025-07-15 17:38:20 - [flatmate.src.fm.modules.categorize.cat_presenter] [ERROR] - Error loading transactions: 'DBIOService' object has no attribute 'column_manager'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 308, in _handle_load_db
    df = self.data_service.get_transactions_dataframe(**filter_kwargs)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 226, in get_transactions_dataframe
    return self._fetch_raw_transactions_df(**filters)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\data_services\db_io_service.py", line 147, in _fetch_raw_transactions_df
    core_columns = [col.db_name for col in self.column_manager.get_display_columns()]
                                           ^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBIOService' object has no attribute 'column_manager'
