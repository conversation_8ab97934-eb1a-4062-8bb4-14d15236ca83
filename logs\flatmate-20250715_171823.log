2025-07-15 17:18:23 - [flatmate.src.fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 17:18:23 - [flatmate.src.fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 17:18:23 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 17:18:23 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 17:18:23 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 17:18:24 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-15 17:18:24 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-15 17:18:24 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-15 17:18:24 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-15 17:18:24 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 17:18:24 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-15 17:18:24 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 17:18:24 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-15 17:18:24 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-15 17:18:24 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-15 17:18:24 - [flatmate.src.fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-15 17:18:24 - [main] [INFO] - Application starting...
2025-07-15 17:18:26 - [main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-15 17:18:26 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-15 17:18:26 - [flatmate.src.fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-15 17:18:26 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Factories
2025-07-15 17:18:26 - [flatmate.src.fm.module_coordinator] [DEBUG] - Registered module factories: ['home', 'update_data', 'categorize']
2025-07-15 17:18:26 - [main] [INFO] - 
=== Preloading Database ===
2025-07-15 17:18:26 - [flatmate.src.fm.core.services.database_preloader_service] [INFO] - Starting database preload into memory...
2025-07-15 17:18:26 - [flatmate.src.fm.core.services.database_preloader_service] [INFO] - Retrieved 2099 transactions from database
2025-07-15 17:18:26 - [flatmate.src.fm.core.services.database_preloader_service] [ERROR] - Error preloading database: 'CacheService' object has no attribute 'set'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\database_preloader_service.py", line 117, in preload_database
    cache_service.set('preloaded_transactions', self._full_dataset)
    ^^^^^^^^^^^^^^^^^
AttributeError: 'CacheService' object has no attribute 'set'. Did you mean: 'get'?
2025-07-15 17:18:26 - [main] [WARNING] - Database preload failed - modules will load data on demand
2025-07-15 17:18:26 - [flatmate.src.fm.module_coordinator] [INFO] - Starting Application
2025-07-15 17:18:26 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Presenter
2025-07-15 17:18:26 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-15 17:18:26 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Module
2025-07-15 17:18:26 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-15 17:18:26 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Module initialization complete
2025-07-15 17:18:26 - [main] [INFO] - 
=== Application Ready ===
