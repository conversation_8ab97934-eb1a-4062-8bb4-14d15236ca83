"""
Home view implementation.
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QPushButton, QLabel, 
                             QSpacerItem, QSizePolicy, QFrame, QGridLayout,
                             QHBoxLayout, QScrollArea)
from PySide6.QtCore import Signal, Qt
from PySide6.QtGui import QPixmap, QFont, QIcon
from pathlib import Path

from ..base.base_module_view import BaseModuleView

class HomeView(BaseModuleView):
    """Home view with navigation buttons."""
    
    #------------------
    # Signals
    #------------------
    update_data_clicked = Signal()
    view_data_clicked = Signal()
    categorize_clicked = Signal()
    settings_clicked = Signal()
    quit_clicked = Signal()
    
    def __init__(self, parent=None):
        """Initialize the view."""
        super().__init__(parent)
        self.show_default_content()
    
    def setup_ui(self):
        """Initial UI setup - called by base class."""
        # Create navigation buttons
        self.profile_btn = QPushButton("Profile")
        self.profile_btn.setObjectName("btn_mngProfile")
        self.profile_btn.setProperty("type", "nav_btn")
        self.profile_btn.clicked.connect(self.settings_clicked.emit)
        
        self.accounts_btn = QPushButton("Account")
        self.accounts_btn.setObjectName("btn_Accounts")
        self.accounts_btn.setProperty("type", "nav_btn")
        self.accounts_btn.clicked.connect(self.view_data_clicked.emit)
        
        self.update_btn = QPushButton("Update")
        self.update_btn.setObjectName("btn_updateData")
        self.update_btn.setProperty("type", "nav_btn")
        self.update_btn.clicked.connect(self.update_data_clicked.emit)
        
        # Categorise button
        self.categorize_btn = QPushButton("Categorise")
        self.categorize_btn.setObjectName("btn_categorize")
        self.categorize_btn.setProperty("type", "nav_btn")
        self.categorize_btn.clicked.connect(self.categorize_clicked.emit)
        
        self.quit_btn = QPushButton("Quit")
        self.quit_btn.setObjectName("btn_quit")
        self.quit_btn.setProperty("type", "exit_btn")
        self.quit_btn.clicked.connect(self.quit_clicked.emit)
        
        # Create content widget
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(0)
        self.content_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Create main layout
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create and set up panels
        self.left_panel, left_layout = self.create_left_panel_container()
        self.setup_left_panel(left_layout)
        main_layout.addWidget(self.left_panel)
        
        self.center_panel, center_layout = self.create_center_panel_container()
        self.setup_center_panel(center_layout)
        main_layout.addWidget(self.center_panel)

    #------------------
    # Required Overrides
    #------------------
    def setup_left_panel(self, layout):
        """Set up the left panel with navigation buttons."""
        layout.setContentsMargins(20, 30, 20, 30)
        layout.setSpacing(25)
        
        layout.addWidget(self.profile_btn)
        layout.addWidget(self.accounts_btn)
        layout.addWidget(self.update_btn)
        layout.addWidget(self.categorize_btn)
        layout.addStretch()
        layout.addWidget(self.quit_btn)
    
    def setup_center_panel(self, layout):
        """Set up the center panel with content area."""
        layout.addWidget(self.content_widget)
    
    def _clear_content(self):
        """Clear the content area."""
        while self.content_layout.count():
            item = self.content_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
    
    def show_default_content(self, content=None):
        """Show the default home content with welcome screen."""
        self._clear_content()
        
        # Create scroll area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setFrameShape(QFrame.Shape.NoFrame)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # Main container
        container = QWidget()
        container_layout = QVBoxLayout(container)
        container_layout.setSpacing(10)  
        container_layout.setContentsMargins(10, 10, 10, 10)  
        
        # Logo and Title Section
        logo_section = self._create_logo_section("Welcome to Flatmate", "Your home for managing finances")
        container_layout.addWidget(logo_section)
        
        # Quick Actions Section
        actions_frame = self._create_actions_section([
            {"title": "Manage Profile", "description": "Update your profile information", "action": "settings"},
            {"title": "View Accounts", "description": "View your account balances and transactions", "action": "view_data"},
            {"title": "Update Data", "description": "Update your financial data", "action": "update_data"}
        ])
        container_layout.addWidget(actions_frame)
        
        # Add stretch and set widget
        container_layout.addStretch()
        scroll.setWidget(container)
        
        self.content_layout.addWidget(scroll)
    
    def _create_logo_section(self, title, subtitle):
        """Create the logo and title section."""
        section = QWidget()
        layout = QVBoxLayout(section)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.setSpacing(10)
        
        # Logo
        logo_label = QLabel()
        logo_path = Path(__file__).parent.parent.parent.parent.parent / "resources" / "images" / "flatmate_logo_standin.jpeg"
        logo_pixmap = QPixmap(str(logo_path))
        if not logo_pixmap.isNull():
            logo_label.setPixmap(logo_pixmap.scaled(
                300, 300,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            ))
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(logo_label)
        
        # Title
        title_label = QLabel(title)
        title_label.setObjectName("heading")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel(subtitle)
        subtitle_label.setObjectName("subheading")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(subtitle_label)
        
        return section
    
    def _create_actions_section(self, actions):
        """Create the quick actions section."""
        frame = QFrame()
        frame.setObjectName("actions_section")
        layout = QGridLayout(frame)
        layout.setSpacing(10)  # Reduced spacing
        
        for i, action in enumerate(actions):
            btn = self._create_action_button(
                action["title"],
                action["description"],
                getattr(self, f"{action['action']}_clicked").emit
            )
            layout.addWidget(btn, i // 2, i % 2)
        
        return frame
    
    def _create_action_button(self, title, description, callback):
        """Create an action button with description."""
        btn_container = QFrame()
        btn_container.setObjectName("action_btn_container")
        btn_layout = QVBoxLayout(btn_container)
        btn_layout.setContentsMargins(0, 0, 0, 0)
        btn_layout.setSpacing(5)
        
        # Button
        btn = QPushButton(title)
        btn.setProperty("type", "nav_btn")
        btn.clicked.connect(callback)
        btn_layout.addWidget(btn)
        
        # Description
        desc = QLabel(description)
        desc.setWordWrap(True)
        desc.setAlignment(Qt.AlignmentFlag.AlignCenter)
        btn_layout.addWidget(desc)
        
        return btn_container
    
    def show_splash_screen(self):
        """Show the splash screen with the standin logo."""
        self._clear_content()
        
        # Load and display the image
        logo_path = Path(__file__).parent.parent.parent.parent.parent / "resources" / "images" / "flatmate_logo_standin.jpeg"
        pixmap = QPixmap(str(logo_path))
        if not pixmap.isNull():
            image_label = QLabel()
            scaled_pixmap = pixmap.scaled(800, 600, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            image_label.setPixmap(scaled_pixmap)
            image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.content_layout.addWidget(image_label)
        else:
            error_label = QLabel("Unable to load splash image")
            error_label.setObjectName("error_message")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.content_layout.addWidget(error_label)
    
    #------------------
    # Main Window Integration
    #------------------
    def setup_in_main_window(self, main_window):
        """Set up the view in the main window."""
        main_window.set_left_panel_content(self.left_panel)
        main_window.set_center_panel_content(self.center_panel)

    #------------------
    # Cleanup
    #------------------
    def cleanup(self):
        """Clean up the view before switching away."""
        print("\n=== Cleaning up Home Module ===")
        # Only disconnect signals that are connected
        for signal in [self.update_data_clicked, self.view_data_clicked,
                      self.settings_clicked,
                      self.quit_clicked]:
            try:
                if signal is not None:  # Check if signal exists before disconnecting
                    signal.disconnect()
            except (TypeError, RuntimeError):
                # Skip if signal wasn't connected
                pass
        print("Home Module cleanup complete")
        if self.main_window:
            self.main_window = None

if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    window = HomeView()
    window.resize(1200, 800)
    window.show()
    
    sys.exit(app.exec_())
