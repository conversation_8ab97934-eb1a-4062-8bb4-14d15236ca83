# Data Caching Architecture Design Document

*Date: 2025-07-15*
*Purpose: Design app-wide data caching strategy for transaction data*

## 🎯 **Core Objective**

Move from database-query-per-request to **memory-centric architecture** where:
- Entire transaction database loads into memory at app startup
- All filtering/querying happens in-memory for instant results
- Database is only hit for writes/updates, not reads

## 🏗️ **Architectural Considerations**

### **Use Cases to Support**

#### **Primary Use Cases**
1. **Categorize Module**: Filter transactions by date/account, instant results
2. **Reports Module** (future): Aggregate data across date ranges, accounts
3. **Search/Filter**: Real-time search across transaction details
4. **Dashboard Views**: Quick stats, recent transactions, summaries

#### **Secondary Use Cases**
1. **Data Import**: New transactions added to both memory and database
2. **Transaction Updates**: Category/tag changes reflected in memory
3. **Account Management**: Account changes update cached data
4. **Backup/Export**: Memory cache can be source for exports

### **Memory Usage Analysis**

#### **Current Database Size**
- **Test Data**: ~2099 transactions
- **Memory Usage**: 0.0MB (essentially negligible)
- **Production Estimate**: Even 100k transactions likely < 50MB

#### **Memory Scalability**
- **Modern Applications**: Browser tabs use 100-500MB routinely
- **Financial Data**: Transaction records are small (date, amount, description, account)
- **Conclusion**: Memory usage is "vanishingly little" as user noted

## 🔧 **Proposed Architecture Options**

### **Option A: Centralized Cache Service**
```
CacheService (existing)
├── TransactionCache
│   ├── full_dataset: DataFrame
│   ├── last_updated: timestamp
│   └── methods: get_filtered(), refresh(), update_record()
└── Other caches...
```

**Pros**: 
- Uses existing cache infrastructure
- Centralized management
- Easy to extend for other data types

**Cons**:
- Cache service is generic, might not be optimized for DataFrame operations
- Potential memory management issues if cache grows large

### **Option B: Dedicated Transaction Data Service**
```
TransactionDataService (new)
├── _master_dataframe: DataFrame
├── _last_refresh: timestamp
├── get_filtered_data(filters) -> DataFrame
├── refresh_from_database() -> bool
├── update_transaction(id, changes) -> bool
└── add_transactions(new_txns) -> bool
```

**Pros**:
- Purpose-built for transaction data
- Optimized DataFrame operations
- Clear API for transaction-specific operations

**Cons**:
- New service to maintain
- Potential duplication with existing services

### **Option C: Enhanced DBIOService**
```
DBIOService (enhanced)
├── _cached_dataframe: DataFrame
├── _cache_enabled: bool
├── get_transactions_dataframe(use_cache=True, **filters)
├── refresh_cache() -> bool
└── existing methods...
```

**Pros**:
- Extends existing service
- Backward compatible
- Single point for all database operations

**Cons**:
- DBIOService becomes more complex
- Mixing database I/O with caching concerns

## 🎯 **Recommended Approach: Option B - Dedicated Service**

### **Rationale**
1. **Single Responsibility**: Service focused solely on transaction data management
2. **Performance**: Optimized for DataFrame operations and filtering
3. **Scalability**: Can add features like indexing, pre-computed aggregations
4. **Testability**: Easier to test caching logic in isolation

### **Proposed API Design**

```python
class TransactionDataService:
    def __init__(self):
        self._master_df: Optional[pd.DataFrame] = None
        self._last_refresh: Optional[datetime] = None
        self._db_service = DBIOService()
    
    # Core data access
    def get_all_data(self) -> pd.DataFrame
    def get_filtered_data(self, **filters) -> pd.DataFrame
    def is_loaded(self) -> bool
    
    # Data management
    def load_from_database(self, show_progress=True) -> bool
    def refresh_data(self) -> bool
    def get_load_info(self) -> Dict[str, Any]
    
    # Data modification (updates both memory and database)
    def update_transaction(self, txn_id: str, updates: Dict) -> bool
    def add_transactions(self, transactions: List[Dict]) -> bool
    def delete_transaction(self, txn_id: str) -> bool
    
    # Utility methods
    def get_unique_accounts(self) -> List[str]
    def get_date_range(self) -> Tuple[date, date]
    def search_transactions(self, search_term: str) -> pd.DataFrame
```

## 🔄 **Data Flow Architecture**

### **Application Startup**
```
main.py
├── Initialize TransactionDataService
├── Call load_from_database()
├── Cache full dataset in memory
└── Continue with app initialization
```

### **Module Data Access**
```
categorize_module
├── Get TransactionDataService instance
├── Call get_filtered_data(start_date=X, account=Y)
├── Receive instant filtered DataFrame
└── Display results
```

### **Data Updates**
```
transaction_update
├── Call update_transaction(id, changes)
├── Service updates database
├── Service updates memory cache
└── All modules see updated data instantly
```

## 🚀 **Implementation Strategy**

### **Phase 1: Core Service**
1. Create `TransactionDataService` class
2. Implement basic load/filter functionality
3. Integrate with main.py startup
4. Update categorize module to use new service

### **Phase 2: Enhanced Features**
1. Add transaction update/insert methods
2. Implement search functionality
3. Add data validation and error handling
4. Performance optimizations (indexing, etc.)

### **Phase 3: App-Wide Integration**
1. Update all modules to use TransactionDataService
2. Remove direct DBIOService calls for reads
3. Add refresh mechanisms for data changes
4. Implement cache invalidation strategies

## 🤔 **Open Questions for Discussion**

### **Service Location**
- Where should `TransactionDataService` live in the codebase?
- `fm.core.services.transaction_data_service`?
- `fm.core.data_services.transaction_data_service`?

### **Singleton vs Instance**
- Should this be a singleton service (like InfoBarService)?
- Or should modules create their own instances?

### **Cache Invalidation**
- How do we handle external database changes?
- Should we periodically refresh from database?
- File system watching for database changes?

### **Error Handling**
- What happens if memory cache becomes corrupted?
- Fallback to database queries?
- Automatic cache rebuild?

### **Configuration**
- Should caching be configurable (on/off)?
- Memory usage limits?
- Refresh intervals?

## 📋 **Next Steps**

1. **Discuss and decide** on architecture approach
2. **Define service interface** and method signatures
3. **Choose implementation location** in codebase
4. **Plan integration strategy** with existing modules
5. **Implement Phase 1** with basic functionality

---

**Ready for discussion and refinement based on your requirements and preferences.**
