"""
Database Caching Service

Acts as an intermediary between DBIOService and the database, loading as much 
of the database into memory as possible and only accessing the database when needed.

This service:
- Loads the entire transaction database into memory at startup (if memory allows)
- Provides instant filtering and querying from memory
- Falls back to database queries when memory cache is not available
- Manages memory usage within configurable limits
"""

import time
import pandas as pd
from datetime import datetime, date
from typing import Optional, Dict, Any, List, Union

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

from fm.core.services.logger import log


class DBCachingService:
    """
    Intermediary service that caches database content in memory for instant access.
    
    Acts as a smart cache between DBIOService and the actual database,
    loading as much data as memory limits allow.
    """
    
    def __init__(self, memory_limit_mb: int = 512):
        """
        Initialize the database caching service.
        
        Args:
            memory_limit_mb: Maximum memory to use for caching (default: 512MB)
        """
        self._memory_limit_mb = memory_limit_mb
        self._cached_df: Optional[pd.DataFrame] = None
        self._cache_loaded = False
        self._load_time: Optional[datetime] = None
        self._memory_usage_mb = 0.0

        # Cached statistics for instant access
        self._cached_stats: Optional[Dict[str, Any]] = None
        
    def initialize_cache(self, db_io_service, show_progress: bool = True) -> bool:
        """
        Initialize the cache by loading data from database via DBIOService.
        
        Args:
            db_io_service: DBIOService instance to load data from
            show_progress: Whether to show loading progress
            
        Returns:
            True if cache loaded successfully, False otherwise
        """
        log("Initializing database cache...", level="info")
        start_time = time.time()
        
        try:
            # Check available memory before loading
            available_mb = self._get_available_memory_mb()
            if available_mb < self._memory_limit_mb:
                log(f"Insufficient memory for caching. Available: {available_mb}MB, Required: {self._memory_limit_mb}MB", 
                    level="warning")
                return False
            
            # Load all transactions from database with ALL columns (no filters = everything)
            log("Loading all transactions from database with all columns...", level="info")
            self._cached_df = db_io_service._fetch_raw_transactions_df(
                all_cols=True,  # Load ALL columns, not just display columns
                only_columns_with_data=False  # Include all columns even if empty
            )

            if self._cached_df.empty:
                log("No transactions found in database", level="warning")
                self._cache_loaded = True
                return True
            
            # Check memory usage
            self._memory_usage_mb = self._calculate_dataframe_memory_mb(self._cached_df)
            
            if self._memory_usage_mb > self._memory_limit_mb:
                log(f"Dataset too large for cache: {self._memory_usage_mb:.1f}MB > {self._memory_limit_mb}MB limit", 
                    level="warning")
                self._cached_df = None
                return False
            
            # Cache successfully loaded
            self._cache_loaded = True
            self._load_time = datetime.now()

            # Calculate and cache basic statistics
            self._calculate_cached_stats()

            elapsed = time.time() - start_time
            log(f"Database cache initialized: {len(self._cached_df)} transactions, "
                f"{self._memory_usage_mb:.1f}MB memory, {elapsed:.1f}s", level="info")

            return True
            
        except Exception as e:
            log(f"Failed to initialize database cache: {e}", level="error")
            self._cached_df = None
            self._cache_loaded = False
            return False
    
    def get_transactions_dataframe(self, db_io_service, **filters) -> pd.DataFrame:
        """
        Get transactions as DataFrame, using cache when available.
        
        This is the main method that DBIOService calls. It will:
        1. Use cached data and filter in memory (if cache loaded)
        2. Fall back to database query (if cache not available)
        
        Args:
            db_io_service: DBIOService instance for database fallback
            **filters: Filters to apply (start_date, end_date, account_number, etc.)
            
        Returns:
            Filtered DataFrame of transactions
        """
        if self._cache_loaded and self._cached_df is not None:
            # Use cached data - filter in memory (instant!)
            log(f"Using cached data for transaction query with filters: {filters}", level="debug")
            return self._filter_cached_data(**filters)
        else:
            # Fall back to database query with same parameters as cache
            log(f"Cache not available, falling back to database query with filters: {filters}", level="debug")
            return db_io_service._fetch_raw_transactions_df(
                all_cols=True,  # Match cache loading
                only_columns_with_data=False,  # Match cache loading
                **filters
            )
    
    def _filter_cached_data(self, **filters) -> pd.DataFrame:
        """
        Filter the cached DataFrame based on provided filters.
        
        Args:
            **filters: Filters to apply
            
        Returns:
            Filtered DataFrame
        """
        if self._cached_df is None:
            return pd.DataFrame()
        
        df = self._cached_df.copy()
        
        # Apply date filters
        if 'start_date' in filters and filters['start_date']:
            start_date = filters['start_date']
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date).date()
            df = df[pd.to_datetime(df['date']).dt.date >= start_date]
        
        if 'end_date' in filters and filters['end_date']:
            end_date = filters['end_date']
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date).date()
            df = df[pd.to_datetime(df['date']).dt.date <= end_date]
        
        # Apply account filter
        if 'account_number' in filters and filters['account_number']:
            df = df[df['account'] == filters['account_number']]
        
        # Apply amount filters
        if 'min_amount' in filters and filters['min_amount'] is not None:
            df = df[df['amount'] >= filters['min_amount']]
        
        if 'max_amount' in filters and filters['max_amount'] is not None:
            df = df[df['amount'] <= filters['max_amount']]
        
        log(f"Filtered cached data: {len(self._cached_df)} → {len(df)} transactions", level="debug")

        # Debug: Log DataFrame info
        if len(df) == 0 and len(self._cached_df) > 0:
            log(f"Warning: Filtering resulted in empty DataFrame. Original columns: {list(self._cached_df.columns)}", level="warning")
            log(f"Applied filters: {filters}", level="warning")
            if 'start_date' in filters or 'end_date' in filters:
                log(f"Date column sample: {self._cached_df['date'].head()}", level="debug")

        return df
    
    def get_unique_account_numbers(self, db_io_service) -> List[str]:
        """
        Get unique account numbers, using cache when available.
        
        Args:
            db_io_service: DBIOService instance for database fallback
            
        Returns:
            List of unique account numbers
        """
        if self._cache_loaded and self._cached_df is not None:
            return sorted(self._cached_df['account'].dropna().unique().tolist())
        else:
            # Fall back to database query
            return db_io_service.get_unique_account_numbers()
    
    def is_cache_loaded(self) -> bool:
        """Check if cache is loaded and ready."""
        return self._cache_loaded and self._cached_df is not None
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        Get information about the current cache state.

        Returns:
            Dictionary with cache statistics
        """
        if not self._cache_loaded:
            return {"loaded": False}

        info = {
            "loaded": True,
            "total_transactions": len(self._cached_df) if self._cached_df is not None else 0,
            "memory_usage_mb": self._memory_usage_mb,
            "memory_limit_mb": self._memory_limit_mb,
            "load_time": self._load_time,
        }

        # Add cached statistics if available
        if self._cached_stats:
            info.update(self._cached_stats)

        return info

    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get cached database statistics for instant access.

        Returns:
            Dictionary with database statistics
        """
        if not self._cache_loaded or not self._cached_stats:
            return {}

        return self._cached_stats.copy()
    
    def refresh_cache(self, db_io_service) -> bool:
        """
        Refresh the cache by reloading from database.
        
        Args:
            db_io_service: DBIOService instance to reload from
            
        Returns:
            True if refresh successful
        """
        log("Refreshing database cache...", level="info")
        self._cached_df = None
        self._cache_loaded = False
        return self.initialize_cache(db_io_service, show_progress=True)
    
    def clear_cache(self):
        """Clear the cache and free memory."""
        log("Clearing database cache...", level="info")
        self._cached_df = None
        self._cache_loaded = False
        self._load_time = None
        self._memory_usage_mb = 0.0
    
    def _get_available_memory_mb(self) -> float:
        """Get available system memory in MB."""
        if PSUTIL_AVAILABLE:
            try:
                memory = psutil.virtual_memory()
                return memory.available / (1024 * 1024)  # Convert to MB
            except Exception:
                pass

        # Fallback if psutil not available
        return 2048.0  # Assume 2GB available
    
    def _calculate_dataframe_memory_mb(self, df: pd.DataFrame) -> float:
        """Calculate memory usage of DataFrame in MB."""
        return df.memory_usage(deep=True).sum() / (1024 * 1024)

    def _calculate_cached_stats(self):
        """Calculate and cache basic database statistics for instant access."""
        if self._cached_df is None or self._cached_df.empty:
            self._cached_stats = {}
            return

        try:
            df = self._cached_df

            # Basic counts
            stats = {
                "total_transactions": len(df),
                "unique_accounts": len(df['account'].dropna().unique()) if 'account' in df.columns else 0,
            }

            # Date range
            if 'date' in df.columns:
                dates = pd.to_datetime(df['date'], errors='coerce').dropna()
                if not dates.empty:
                    stats["date_range_start"] = dates.min().date()
                    stats["date_range_end"] = dates.max().date()
                    stats["date_span_days"] = (dates.max() - dates.min()).days

            # Amount statistics
            if 'amount' in df.columns:
                amounts = pd.to_numeric(df['amount'], errors='coerce').dropna()
                if not amounts.empty:
                    stats["total_amount"] = float(amounts.sum())
                    stats["avg_amount"] = float(amounts.mean())
                    stats["min_amount"] = float(amounts.min())
                    stats["max_amount"] = float(amounts.max())

            # Account breakdown
            if 'account' in df.columns:
                account_counts = df['account'].value_counts().to_dict()
                stats["transactions_by_account"] = account_counts

            self._cached_stats = stats
            log(f"Cached database statistics: {stats['total_transactions']} transactions, "
                f"{stats.get('unique_accounts', 0)} accounts", level="debug")

        except Exception as e:
            log(f"Error calculating cached stats: {e}", level="warning")
            self._cached_stats = {}
    
    @staticmethod
    def get_recommended_memory_limit_mb() -> int:
        """
        Get recommended memory limit based on system resources.

        Returns:
            Recommended memory limit in MB (default: 512MB or 25% of available RAM, whichever is smaller)
        """
        if PSUTIL_AVAILABLE:
            try:
                memory = psutil.virtual_memory()
                available_mb = memory.available / (1024 * 1024)

                # Use 25% of available RAM or 512MB, whichever is smaller
                recommended = min(512, int(available_mb * 0.25))

                # Ensure minimum of 64MB
                return max(64, recommended)

            except Exception:
                pass

        # Fallback if psutil not available
        return 512
