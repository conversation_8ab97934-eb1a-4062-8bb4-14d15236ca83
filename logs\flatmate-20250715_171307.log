2025-07-15 17:13:07 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 17:13:07 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 17:13:07 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 17:13:07 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-15 17:13:07 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-15 17:13:07 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-15 17:13:07 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-15 17:13:07 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 17:13:07 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-15 17:13:07 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 17:13:07 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-15 17:13:07 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-15 17:13:07 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-15 17:13:08 - [flatmate.src.fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-15 17:13:08 - [main] [INFO] - Application starting...
2025-07-15 17:13:11 - [main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-15 17:13:11 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-15 17:13:11 - [flatmate.src.fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-15 17:13:11 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Factories
2025-07-15 17:13:11 - [flatmate.src.fm.module_coordinator] [DEBUG] - Registered module factories: ['home', 'update_data', 'categorize']
2025-07-15 17:13:11 - [main] [INFO] - 
=== Preloading Database ===
2025-07-15 17:13:11 - [flatmate.src.fm.core.services.database_preloader_service] [INFO] - Starting database preload into memory...
2025-07-15 17:13:11 - [flatmate.src.fm.core.services.database_preloader_service] [INFO] - Retrieved 2099 transactions from database
2025-07-15 17:13:11 - [flatmate.src.fm.core.services.database_preloader_service] [ERROR] - Error preloading database: 'Transaction' object has no attribute 'account_number'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\database_preloader_service.py", line 86, in preload_database
    'account': txn.account_number,
               ^^^^^^^^^^^^^^^^^^
AttributeError: 'Transaction' object has no attribute 'account_number'
2025-07-15 17:13:11 - [main] [WARNING] - Database preload failed - modules will load data on demand
2025-07-15 17:13:11 - [flatmate.src.fm.module_coordinator] [INFO] - Starting Application
2025-07-15 17:13:11 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Presenter
2025-07-15 17:13:11 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-15 17:13:11 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Module
2025-07-15 17:13:11 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-15 17:13:11 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Module initialization complete
2025-07-15 17:13:11 - [main] [INFO] - 
=== Application Ready ===
