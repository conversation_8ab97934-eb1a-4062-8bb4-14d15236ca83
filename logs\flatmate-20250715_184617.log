2025-07-15 18:46:17 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-15 18:46:17 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-15 18:46:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-15 18:46:17 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-15 18:46:17 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-15 18:46:18 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-15 18:46:18 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-15 18:46:18 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 18:46:18 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-15 18:46:18 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-15 18:46:18 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-15 18:46:18 - [flatmate.src.fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-15 18:46:18 - [main] [INFO] - Application starting...
2025-07-15 18:46:20 - [main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-15 18:46:20 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-15 18:46:20 - [flatmate.src.fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-15 18:46:20 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Factories
2025-07-15 18:46:20 - [flatmate.src.fm.module_coordinator] [DEBUG] - Registered module factories: ['home', 'update_data', 'categorize']
2025-07-15 18:46:20 - [main] [INFO] - 
=== Initializing Database Cache ===
2025-07-15 18:46:20 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 18:46:20 - [flatmate.src.fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 330MB cache limit
2025-07-15 18:46:20 - [flatmate.src.fm.core.data_services.cache.db_caching] [INFO] - Initializing database cache...
2025-07-15 18:46:20 - [flatmate.src.fm.core.data_services.cache.db_caching] [INFO] - Loading all transactions from database with all columns...
2025-07-15 18:46:21 - [flatmate.src.fm.core.data_services.cache.db_caching] [DEBUG] - Cached database statistics: 2099 transactions, 3 accounts
2025-07-15 18:46:21 - [flatmate.src.fm.core.data_services.cache.db_caching] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory, 0.7s
2025-07-15 18:46:21 - [main] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory usage
2025-07-15 18:46:21 - [flatmate.src.fm.module_coordinator] [INFO] - Starting Application
2025-07-15 18:46:21 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Presenter
2025-07-15 18:46:21 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-15 18:46:21 - [flatmate.src.fm.modules.home.home_presenter] [INFO] - Initializing Home Module
2025-07-15 18:46:21 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-15 18:46:21 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Module initialization complete
2025-07-15 18:46:21 - [main] [INFO] - 
=== Application Ready ===
2025-07-15 18:46:37 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Module cleanup complete
2025-07-15 18:46:37 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 18:46:37 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 369MB cache limit
2025-07-15 18:46:37 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-07-15 18:46:37 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-07-15 18:46:37 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-07-15 18:46:38 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-15 18:46:38 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 351MB cache limit
2025-07-15 18:46:38 - [fm.modules.base.base_module_view] [INFO] - Setting up CatView in Main Window
2025-07-15 18:46:38 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-15 18:46:38 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-15 18:46:38 - [fm.modules.base.base_module_view] [INFO] - CatView setup complete
2025-07-15 18:46:38 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-15 18:46:38 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-15 18:46:38 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_account = None (Source: cat_presenter.py:initialize)
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Auto-loading ALL transactions from database...
2025-07-15 18:46:38 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._handle_load_db
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from database for categorisation…
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Filters: None
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {}
2025-07-15 18:46:38 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Data Retrieval from Cache
2025-07-15 18:46:38 - [fm.core.data_services.cache.db_caching] [DEBUG] - Cache not available, falling back to database query with filters: {}
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Retrieved 2099 transactions as DataFrame
2025-07-15 18:46:38 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Data Retrieval from Cache took 0.425s
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame shape: (2099, 30), empty: False
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'import_date', 'modified_date', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id']
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - First few rows:
              account  amount  ...  tp_ref unique_id
0  38-9004-0646977-04  -30.44  ...    None      None
1  38-9004-0646977-04   -9.40  ...    None      None
2  38-9004-0646977-04  -18.99  ...    None      None
3  38-9004-0646977-04  -20.50  ...    None      None
4  38-9004-0646977-00 -100.00  ...    None      None

[5 rows x 30 columns]
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Using DataFrame with shape: (2099, 30)
2025-07-15 18:46:38 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Transaction Categorization
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Applying categorization to transactions...
2025-07-15 18:46:38 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Transaction Categorization took 0.057s
2025-07-15 18:46:38 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._apply_default_sorting
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Applied default sorting: date (descending)
2025-07-15 18:46:38 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._apply_default_sorting took 0.006s
2025-07-15 18:46:38 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Table View Data Setting
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize.cat_presenter] [DEBUG] - Setting DataFrame with 2099 transactions to view
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize._view.cat_view] [DEBUG] - CatView setting dataframe: 2099 rows
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize._view.cat_view] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'import_date', 'modified_date', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id']
2025-07-15 18:46:38 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting transactions: 2099 rows
2025-07-15 18:46:41 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Displaying columns: ['Account', 'Date', 'Details', 'Amount', 'Category', 'Tags', 'Notes']
2025-07-15 18:46:41 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Table View Data Setting took 2.742s
2025-07-15 18:46:41 - [flatmate.src.fm.modules.categorize.cat_presenter] [INFO] - Successfully loaded and displayed 2099 transactions in 3.3s (638.4 txns/s)
2025-07-15 18:46:41 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._handle_load_db took 3.290s
