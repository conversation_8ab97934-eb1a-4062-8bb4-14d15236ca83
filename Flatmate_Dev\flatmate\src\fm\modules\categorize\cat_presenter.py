"""Categorize module presenter (MVP stub).

Responsibilities:
* Instantiate the CatView
* Delegate file selection and categorisation to core.processor
* Handle navigation back to home or other modules
* Wire view signals to internal handlers
"""
from __future__ import annotations
from pathlib import Path
from typing import List
from datetime import datetime

import pandas as pd

from datetime import datetime
import time

from fm.core.data_services import DBIOService
from fm.core.services.event_bus import global_event_bus, Events
from fm.core.utils.timing_decorator import timing_decorator, timing_context

from .core.categorizer import TransactionCategorizer
from ._view.cat_view import CatView
from ...core.services.logger import log
from ...core.services.cache_service import cache_service
from ...gui.services.info_bar_service import InfoBarService
from .utils.transaction_utils import transactions_to_dataframe

class CategorizePresenter:
    """Presenter for the Categorize module (stub version)."""

    def __init__(self, main_window):
        self.main_window = main_window
        self.view = CatView(main_window)
        self.data_service = DBIOService()
        self._cat = TransactionCategorizer()
        self.info_bar_service = InfoBarService.get_instance()
        self._connect_signals()
        self._original_df = None
        self._modified = False

    # ------------------------------------------------------------------
    def _connect_signals(self):
        self.view.cancel_clicked.connect(lambda: self.request_transition("home"))
        self.view.files_selected.connect(self._handle_files_selected)
        self.view.load_db_requested.connect(self._handle_load_db)
        self.view.transaction_selected.connect(self._handle_transaction_selected)
        self.view.tags_updated.connect(self._handle_tags_updated)

    # ------------------------------------------------------------------
    def initialize(self):
        """Setup view in the main window."""
        self.view.setup_in_main_window(self.main_window)
        self.main_window.show_left_panel()



        # Import config for database loading preferences
        from .config import config

        # Set up database loading defaults
        config.ensure_defaults({
            'categorize.database.default_load_from_db': True,
            'categorize.database.remember_last_query': True,
            'categorize.database.auto_load_on_startup': True,
            'categorize.database.default_sort_column': 'date',
            'categorize.database.default_sort_order': 'descending',

            # Define keys for last used filters (will be None initially)
            'categorize.database.last_start_date': None,
            'categorize.database.last_end_date': None,
            'categorize.database.last_account': None,
        })

        # Check if we should auto-load from database
        should_auto_load = config.get_value('categorize.database.auto_load_on_startup')
        load_from_db = config.get_value('categorize.database.default_load_from_db')

        if should_auto_load and load_from_db:
            # Try to load with last used query/filters
            self._auto_load_from_database()
        else:
            # Fallback: Check for cached transactions
            if cache_service.has('categorize_transactions'):
                df = cache_service.get('categorize_transactions')
                self._original_df = df.copy()
                self.view.set_dataframe(df)

    # ------------------------------------------------------------------
    def _auto_load_from_database(self):
        """Auto-load ALL transactions from database (no filters)."""
        log("Auto-loading ALL transactions from database...", level="info")

        # Load ALL transactions - no filters applied
        # Memory usage is negligible (2.3MB for 2099 transactions)
        # Let table view handle filtering for better user experience
        self._handle_load_db(filters=None)  # No filters = load everything

    def _get_last_used_filters(self):
        """Get the last used database query filters with validation."""
        from .config import config

        # Try to get last used filters from config
        last_start_date = config.get_user_preference('categorize.database.last_start_date')
        last_end_date = config.get_user_preference('categorize.database.last_end_date')
        last_account = config.get_user_preference('categorize.database.last_account')

        # Validate that the last account still exists in the database
        if last_account:
            try:
                from fm.core.data_services.db_io_service import DBIOService
                db_service = DBIOService()
                available_accounts = db_service.get_unique_account_numbers()

                if last_account not in available_accounts:
                    log(f"Last used account '{last_account}' no longer exists in database. Clearing account filter.", level="warning")
                    last_account = None
                    # Clear the stale preference
                    config.set_user_preference('categorize.database.last_account', None)

            except Exception as e:
                log(f"Error validating last account: {e}. Clearing account filter.", level="warning")
                last_account = None

        if last_start_date or last_end_date or last_account:
            filters = {}
            if last_start_date:
                # Convert string back to date if needed
                if isinstance(last_start_date, str):
                    from datetime import datetime
                    last_start_date = datetime.fromisoformat(last_start_date).date()
                filters['start_date'] = last_start_date
            if last_end_date:
                if isinstance(last_end_date, str):
                    from datetime import datetime
                    last_end_date = datetime.fromisoformat(last_end_date).date()
                filters['end_date'] = last_end_date
            if last_account:
                filters['account'] = last_account
            return filters

        return None

    def _get_default_filters(self):
        """Get default database query filters."""
        from .config import config
        from datetime import datetime, timedelta

        # Set up default filter config
        config.ensure_defaults({
            'categorize.filters.default_days_back': 30,
            'categorize.filters.use_default_date_range': True,
        })

        filters = {}

        # Add default date range if configured
        use_default_range = config.get_value('categorize.filters.use_default_date_range')
        if use_default_range:
            days_back = config.get_value('categorize.filters.default_days_back')
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days_back)

            filters['start_date'] = start_date
            filters['end_date'] = end_date

        return filters

    def _save_last_used_filters(self, filters):
        """Save the current filters as last used for next time."""
        from .config import config

        remember_last = config.get_value('categorize.database.remember_last_query')
        if not remember_last:
            return

        # Save filters as user preferences
        if filters:
            if 'start_date' in filters and filters['start_date']:
                # Convert date to string for storage
                date_str = filters['start_date'].isoformat() if hasattr(filters['start_date'], 'isoformat') else str(filters['start_date'])
                config.set_user_preference('categorize.database.last_start_date', date_str)

            if 'end_date' in filters and filters['end_date']:
                date_str = filters['end_date'].isoformat() if hasattr(filters['end_date'], 'isoformat') else str(filters['end_date'])
                config.set_user_preference('categorize.database.last_end_date', date_str)

            if 'account' in filters and filters['account']:
                config.set_user_preference('categorize.database.last_account', filters['account'])

        log("Saved last used filters for next session", level="debug")

    # ------------------------------------------------------------------
    def _handle_files_selected(self, file_paths: List[str]):
        log(f"CategorizePresenter received {len(file_paths)} file(s)", level="info")
        
        # Notify start of file processing
        start_time = time.time()
        global_event_bus.publish(
            Events.INFO_MESSAGE,
            {
                "text": f"Processing {len(file_paths)} file(s)...",
                "is_loading": True,
                "progress": 0,
                "total": len(file_paths)
            }
        )
        
        try:
            # Process files with progress updates
            df = None
            for i, file_path in enumerate(file_paths, 1):
                # Update progress for each file
                self.info_bar_service.publish_message(
                    f"Processing file {i} of {len(file_paths)}: {Path(file_path).name}",
                    is_loading=True,
                    progress=i,
                    total=len(file_paths)
                )
                
                # Process the current file
                file_df = categorize_files([file_path])
                
                # Combine with previous files if needed
                if df is None:
                    df = file_df
                else:
                    df = pd.concat([df, file_df], ignore_index=True)
            
            if df is not None:
                self._original_df = df.copy()
                self.view.set_dataframe(df)
                self._modified = False
                
                # Calculate and log performance
                elapsed = time.time() - start_time
                log(f"Successfully processed {len(file_paths)} file(s) with {len(df)} total transactions in {elapsed:.1f}s", 
                    level="info")
                
                # Show completion message
                self.info_bar_service.publish_message(
                    f"Loaded {len(df)} transactions from {len(file_paths)} file(s)",
                    is_loading=False,
                    progress=len(file_paths),
                    total=len(file_paths)
                )
            else:
                log("No data was loaded from the files", level="warning")
                self.info_bar_service.publish_warning("No data was loaded from the selected files")
                
        except Exception as e:
            error_msg = f"Error processing files: {str(e)}"
            log(error_msg, level="error")
            
            # Show error in InfoBar
            global_event_bus.publish(
                Events.INFO_MESSAGE,
                {
                    "text": error_msg,
                    "is_loading": False,
                    "is_error": True
                }
            )
            raise

    # ------------------------------------------------------------------
    @timing_decorator
    def _handle_load_db(self, filters=None):
        """Enhanced database loading with filter persistence and default sorting."""
        from .config import config

        log("Loading transactions from database for categorisation…", level="info")
        log(f"Filters: {filters}", level="debug")

        start_time = time.time()

        # Notify start of loading
        self.info_bar_service.publish_loading("Loading transactions...", progress=0, total=1)

        # Save filters for next time if configured
        if filters:
            self._save_last_used_filters(filters)

        # Apply filters if provided
        filter_kwargs = {}
        if filters:
            if 'start_date' in filters:
                filter_kwargs['start_date'] = filters['start_date']
            if 'end_date' in filters:
                filter_kwargs['end_date'] = filters['end_date']
            if 'account' in filters:
                filter_kwargs['account_number'] = filters['account']

        log(f"Fetching transactions with filters: {filter_kwargs}", level="debug")

        try:
            # Get transactions directly as DataFrame (much simpler!)
            with timing_context("Data Retrieval from Cache"):
                df = self.data_service.get_transactions_dataframe(**filter_kwargs)
                total_txns = len(df)
                log(f"Retrieved {total_txns} transactions as DataFrame", level="info")

            # Debug: Log DataFrame details
            log(f"DataFrame shape: {df.shape}, empty: {df.empty}", level="debug")
            if not df.empty:
                log(f"DataFrame columns: {list(df.columns)}", level="debug")
                log(f"First few rows:\n{df.head()}", level="debug")

            # Update progress with actual count
            if total_txns > 0:
                global_event_bus.publish(
                    Events.INFO_MESSAGE,
                    {
                        "text": f"Processing {total_txns} transactions...",
                        "is_loading": True,
                        "progress": 0,
                        "total": total_txns
                    }
                )

            if df.empty:
                log("No transactions found matching the criteria", level="warning")
                # DataFrame is already empty, just ensure it has expected columns
                if df.empty:
                    df = pd.DataFrame(columns=[
                        'date', 'details', 'amount', 'balance', 'account',
                        'source_uid', 'category', 'tags', 'notes', 'is_processed'
                    ])
                self.view.set_dataframe(df)
                return

            # DataFrame is already ready from db_io_service
            log(f"Using DataFrame with shape: {df.shape}", level="debug")

            # Ensure required columns exist (they should already be there from preloader)
            if "category" not in df.columns:
                df["category"] = ""
            if "tags" not in df.columns:
                log("Adding missing 'tags' column", level="debug")
                df["tags"] = ""
                
            # Apply categorization with progress updates
            with timing_context("Transaction Categorization"):
                log("Applying categorization to transactions...", level="debug")

                # Update progress every N transactions to avoid flooding the event bus
                update_interval = max(1, len(df) // 10)  # Update at most 10 times

                def categorize_with_progress(row):
                    # Update progress periodically
                    if row.name % update_interval == 0:
                        progress = row.name + 1  # +1 because row.name is 0-based
                        global_event_bus.publish(
                            Events.INFO_MESSAGE,
                            {
                                "text": f"Processing {min(progress, total_txns)}/{total_txns} transactions...",
                                "is_loading": True,
                                "progress": progress,
                                "total": total_txns
                            }
                        )
                    return self._cat.categorize_row(row)

                df["category"] = df.apply(categorize_with_progress, axis=1)

            # Apply default sorting (addresses hit list issue)
            df = self._apply_default_sorting(df)

            with timing_context("Table View Data Setting"):
                log(f"Setting DataFrame with {len(df)} transactions to view", level="debug")
                self._original_df = df.copy()
                self.view.set_dataframe(df)
                self._modified = False
            
            # Calculate and log performance
            elapsed = time.time() - start_time
            rate = len(df) / elapsed if elapsed > 0 else 0
            
            log(f"Successfully loaded and displayed {len(df)} transactions in {elapsed:.1f}s ({rate:.1f} txns/s)", 
                level="info")
                
            # Show completion message
            self.info_bar_service.publish_message(
                f"Loaded {len(df)} transactions in {elapsed:.1f}s",
                is_loading=False,
                progress=len(df),
                total=len(df)
            )
            
        except Exception as e:
            error_msg = f"Error loading transactions: {str(e)}"
            log(error_msg, level="error")
            
            # Show error in InfoBar
            self.info_bar_service.publish_error(error_msg)
            raise

    @timing_decorator
    def _apply_default_sorting(self, df):
        """Apply default sorting to the DataFrame (addresses hit list issue)."""
        from .config import config

        # Get default sort configuration using proper config methods
        sort_column = config.get_value('categorize.display.default_sort_column') or 'date'
        sort_ascending = config.get_value('categorize.display.default_sort_ascending') or False

        if sort_column in df.columns:
            df = df.sort_values(by=sort_column, ascending=sort_ascending)
            log(f"Applied default sorting: {sort_column} ({'ascending' if sort_ascending else 'descending'})", level="debug")

        return df

    # ------------------------------------------------------------------
    def _handle_transaction_selected(self, transaction_id):
        """Handle transaction selection."""
        # This could be used to show details in a separate panel
        log(f"Transaction selected: {transaction_id}")
        
    # ------------------------------------------------------------------
    def _handle_tags_updated(self, transaction_id, tags):
        """Handle tags update from the view."""
        if self._original_df is not None:
            # Find the row with this transaction ID
            idx = self._original_df.index[
                self._original_df['id'] == transaction_id
            ].tolist()
            
            if idx:
                # Update the tags in the DataFrame
                self._original_df.at[idx[0], 'tags'] = tags
                self._modified = True
                
                # Update in database
                self.data_service.update_transaction_tags(transaction_id, tags)

    # ------------------------------------------------------------------
    def request_transition(self, target_view: str):
        """Stub – replaced by ModuleCoordinator._connect_module_transitions"""
        pass

    # ------------------------------------------------------------------
    def cleanup(self):
        """Clean up resources before transitioning away."""
        # Cache current transactions if modified
        if self._modified and self._original_df is not None:
            cache_service.put('categorize_transactions', self._original_df)
            
        if self.view:
            self.view.cleanup()




